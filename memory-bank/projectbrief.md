# ПРОЕКТ: Virtualization Controller - Clone Operation

## ОБЗОР ПРОЕКТА
Система виртуализации на базе Kubernetes с поддержкой операций над виртуальными машинами.

## ТЕКУЩАЯ ЗАДАЧА
Реализация функциональности клонирования виртуальных машин (Clone Operation) в рамках VirtualMachineOperation API.

## КЛЮЧЕВЫЕ КОМПОНЕНТЫ
- VirtualMachineOperation API (v1alpha2)
- Clone Service и Step-based архитектура
- Система восстановления (Restorer) с поддержкой клонирования
- Обработка ошибок клонирования

## ТЕХНИЧЕСКИЙ СТЕК
- Go (Kubernetes Controller)
- Kubernetes API Extensions (CRDs)
- Controller-runtime framework
- Step-based execution pattern

## СТАТУС
Ветка: feat/vmop/clone-operation
Фаза: Активная разработка
