# ТЕХНИЧЕСКИЙ КОНТЕКСТ

## АРХИТЕКТУРА СИСТЕМЫ
- **Kubernetes Controller Pattern**: Использование controller-runtime для управления CRDs
- **Step-based Execution**: Пошаговое выполнение операций через StepTaker pattern
- **Resource Management**: Управление виртуальными ресурсами (VM, VD, VMBDA, VMIP)

## КЛЮЧЕВЫЕ КОМПОНЕНТЫ

### VirtualMachineOperation API
- **Типы операций**: Start, Stop, Restart, Migrate, Evict, Restore, Clone
- **Режимы восстановления**: DryRun, Strict, BestEffort
- **Статусы**: Pending, InProgress, Completed, Failed, Terminating

### Clone Operation Architecture
- **Service Layer**: CloneOperation struct с методами Execute, IsApplicable
- **Step Layer**: Валидация, создание снимка, обработка клонирования
- **Restorer Layer**: Управление ресурсами снимка и их клонирование

### Error Handling
- Специализированные ошибки для конфликтов ресурсов
- Форматированные сообщения об ошибках
- Проверка существования и привязки ресурсов

## ПАТТЕРНЫ РАЗРАБОТКИ
- **Condition Management**: Управление статусами через metav1.Condition
- **Resource Validation**: Проверка совместимости и конфликтов
- **Event Recording**: Логирование событий для отладки
