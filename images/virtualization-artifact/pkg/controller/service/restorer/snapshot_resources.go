/*
Copyright 2025 Flant JSC

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package restorer

import (
	"context"
	"errors"
	"fmt"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/deckhouse/virtualization-controller/pkg/common/object"
	"github.com/deckhouse/virtualization-controller/pkg/controller/service/restorer/common"
	restorer "github.com/deckhouse/virtualization-controller/pkg/controller/service/restorer/restorers"
	"github.com/deckhouse/virtualization/api/core/v1alpha2"
)

type SnapshotResources struct {
	uuid           string
	client         client.Client
	restorer       *SecretRestorer
	restorerSecret *corev1.Secret
	vmSnapshot     *v1alpha2.VirtualMachineSnapshot
	objectHandlers []ObjectHandler
	statuses       []SnapshotResourceStatus
	mode           v1alpha2.VMOPRestoreMode
	kind           common.OperationKind
}

type SnapshotResourceStatus struct {
	APIVersion string
	Kind       string
	Name       string
	Status     string
	Message    string
}

func NewSnapshotResources(client client.Client, kind common.OperationKind, mode v1alpha2.VMOPRestoreMode, restorerSecret *corev1.Secret, vmSnapshot *v1alpha2.VirtualMachineSnapshot, uuid string) SnapshotResources {
	return SnapshotResources{
		mode:           mode,
		kind:           kind,
		uuid:           uuid,
		client:         client,
		restorer:       NewSecretRestorer(client),
		vmSnapshot:     vmSnapshot,
		restorerSecret: restorerSecret,
	}
}

func (r *SnapshotResources) Prepare(ctx context.Context) error {
	provisioner, err := r.restorer.RestoreProvisioner(ctx, r.restorerSecret)
	if err != nil {
		return err
	}

	vm, err := r.restorer.RestoreVirtualMachine(ctx, r.restorerSecret)
	if err != nil {
		return err
	}

	vmip, err := r.restorer.RestoreVirtualMachineIPAddress(ctx, r.restorerSecret)
	if err != nil {
		return err
	}

	if vmip != nil {
		vm.Spec.VirtualMachineIPAddress = vmip.Name
	}

	vds, err := getVirtualDisks(ctx, r.client, r.vmSnapshot)
	if err != nil {
		return err
	}

	vmbdas, err := r.restorer.RestoreVirtualMachineBlockDeviceAttachments(ctx, r.restorerSecret)
	if err != nil {
		return err
	}

	for _, vd := range vds {
		r.objectHandlers = append(r.objectHandlers, restorer.NewVirtualDiskHandler(r.client, *vd, r.uuid))
	}

	for _, vmbda := range vmbdas {
		r.objectHandlers = append(r.objectHandlers, restorer.NewVMBlockDeviceAttachmentHandler(r.client, *vmbda, r.uuid))
	}

	if provisioner != nil {
		r.objectHandlers = append(r.objectHandlers, restorer.NewProvisionerHandler(r.client, *provisioner, r.uuid))
	}

	r.objectHandlers = append(r.objectHandlers, restorer.NewVirtualMachineHandler(r.client, *vm, string(r.vmSnapshot.UID), r.mode))

	return nil
}

func (r *SnapshotResources) Validate(ctx context.Context) ([]SnapshotResourceStatus, error) {
	var hasErrors bool

	r.statuses = make([]SnapshotResourceStatus, 0, len(r.objectHandlers))

	for _, ov := range r.objectHandlers {
		obj := ov.Object()

		status := SnapshotResourceStatus{
			APIVersion: obj.GetObjectKind().GroupVersionKind().Version,
			Kind:       obj.GetObjectKind().GroupVersionKind().Kind,
			Name:       obj.GetName(),
			Status:     "ReadyForRestore",
			Message:    obj.GetName() + " is valid for restore",
		}

		if r.kind == common.RestoreKind {
			err := ov.ValidateRestore(ctx)
			switch {
			case err == nil:
			case shouldIgnoreError(r.mode, err):
			default:
				hasErrors = true
				status.Status = "Failed"
				status.Message = err.Error()
			}
		}
		r.statuses = append(r.statuses, status)
	}

	if hasErrors {
		return r.statuses, errors.New("fail to validate the resources: check the status")
	}

	return r.statuses, nil
}

func (r *SnapshotResources) Process(ctx context.Context) ([]SnapshotResourceStatus, error) {
	var hasErrors bool

	r.statuses = make([]SnapshotResourceStatus, 0, len(r.objectHandlers))

	if r.mode == v1alpha2.VMOPRestoreModeDryRun {
		return r.statuses, errors.New("cannot Process with DryRun operation")
	}

	for _, ov := range r.objectHandlers {
		obj := ov.Object()

		status := SnapshotResourceStatus{
			APIVersion: obj.GetObjectKind().GroupVersionKind().Version,
			Kind:       obj.GetObjectKind().GroupVersionKind().Kind,
			Name:       obj.GetName(),
			Status:     "Restored",
			Message:    "",
		}

		if r.kind == common.RestoreKind {
			err := ov.ProcessRestore(ctx)
			switch {
			case err == nil:
			case shouldIgnoreError(r.mode, err):
			case isRetryError(err):
				status.Status = "InProgress"
				status.Message = err.Error()
			default:
				hasErrors = true
				status.Status = "Failed"
				status.Message = err.Error()
			}
		}
		r.statuses = append(r.statuses, status)
	}

	if hasErrors {
		return r.statuses, errors.New("fail to process the resources: check the status")
	}

	return r.statuses, nil
}

var DryRunIgnoredErrors = []error{
	common.ErrVMMaintenanceCondNotFound,
	common.ErrVMNotInMaintenance,
}

var BestEffortIgnoredErrors = []error{
	common.ErrVirtualImageNotFound,
	common.ErrClusterVirtualImageNotFound,
	common.ErrSecretHasDifferentData,
}

var RetryErrors = []error{
	common.ErrRestoring,
	common.ErrUpdating,
	common.ErrWaitingForDeletion,
}

func shouldIgnoreError(mode v1alpha2.VMOPRestoreMode, err error) bool {
	switch mode {
	case v1alpha2.VMOPRestoreModeDryRun:
		for _, e := range DryRunIgnoredErrors {
			if errors.Is(err, e) {
				return true
			}
		}
	case v1alpha2.VMOPRestoreModeBestEffort:
		for _, e := range BestEffortIgnoredErrors {
			if errors.Is(err, e) {
				return true
			}
		}
	}

	return false
}

func isRetryError(err error) bool {
	for _, e := range RetryErrors {
		if errors.Is(err, e) {
			return true
		}
	}
	return false
}

func getVirtualDisks(ctx context.Context, client client.Client, vmSnapshot *v1alpha2.VirtualMachineSnapshot) ([]*v1alpha2.VirtualDisk, error) {
	vds := make([]*v1alpha2.VirtualDisk, 0, len(vmSnapshot.Status.VirtualDiskSnapshotNames))

	for _, vdSnapshotName := range vmSnapshot.Status.VirtualDiskSnapshotNames {
		vdSnapshotKey := types.NamespacedName{Namespace: vmSnapshot.Namespace, Name: vdSnapshotName}
		vdSnapshot, err := object.FetchObject(ctx, vdSnapshotKey, client, &v1alpha2.VirtualDiskSnapshot{})
		if err != nil {
			return nil, fmt.Errorf("failed to fetch the virtual disk snapshot %q: %w", vdSnapshotKey.Name, err)
		}

		if vdSnapshot == nil {
			return nil, fmt.Errorf("the virtual disk snapshot %q %w", vdSnapshotName, common.ErrVirtualDiskSnapshotNotFound)
		}

		vd := v1alpha2.VirtualDisk{
			TypeMeta: metav1.TypeMeta{
				Kind:       v1alpha2.VirtualDiskKind,
				APIVersion: v1alpha2.Version,
			},
			ObjectMeta: metav1.ObjectMeta{
				Name:      vdSnapshot.Spec.VirtualDiskName,
				Namespace: vdSnapshot.Namespace,
			},
			Spec: v1alpha2.VirtualDiskSpec{
				DataSource: &v1alpha2.VirtualDiskDataSource{
					Type: v1alpha2.DataSourceTypeObjectRef,
					ObjectRef: &v1alpha2.VirtualDiskObjectRef{
						Kind: v1alpha2.VirtualDiskObjectRefKindVirtualDiskSnapshot,
						Name: vdSnapshot.Name,
					},
				},
			},
			Status: v1alpha2.VirtualDiskStatus{
				AttachedToVirtualMachines: []v1alpha2.AttachedVirtualMachine{
					{Name: vmSnapshot.Spec.VirtualMachineName, Mounted: true},
				},
			},
		}

		vds = append(vds, &vd)
	}

	return vds, nil
}
