{"models": {"main": {"provider": "claude-code", "modelId": "sonnet", "maxTokens": 8000, "temperature": 0.1}, "research": {"provider": "claude-code", "modelId": "sonnet", "maxTokens": 8000, "temperature": 0.1}, "fallback": {"provider": "claude-code", "modelId": "sonnet", "maxTokens": 8000, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultNumTasks": 10, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "responseLanguage": "English", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "userId": "**********"}, "claudeCode": {}}